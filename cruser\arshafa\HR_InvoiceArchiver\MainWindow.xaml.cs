using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media.Animation;
using HR_InvoiceArchiver.Services;
using HR_InvoiceArchiver.Pages;
using Microsoft.Extensions.DependencyInjection;

namespace HR_InvoiceArchiver
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        private readonly IToastService _toastService;
        private readonly INavigationService _navigationService;
        private readonly ISuccessNotificationService _successNotificationService;

        public MainWindow()
        {
            // Initialize services first to avoid nullability warnings
            _toastService = App.ServiceProvider.GetRequiredService<IToastService>();
            _navigationService = App.ServiceProvider.GetRequiredService<INavigationService>();
            _successNotificationService = App.ServiceProvider.GetRequiredService<ISuccessNotificationService>();

            try
            {
                InitializeComponent();

                // تحسين عرض النافذة
                CenterWindowOnScreen();

                // Subscribe to navigation events
                _navigationService.Navigated += OnNavigated;

                // Subscribe to window loaded event
                Loaded += MainWindow_Loaded;

                // Initialize modern UI features
                InitializeModernFeatures();
            }
            catch (Exception ex)
            {
                // Log critical startup errors
                System.Diagnostics.Debug.WriteLine($"خطأ في تهيئة النافذة الرئيسية: {ex.Message}");

                // Only show MessageBox for critical startup errors that prevent app from working
                if (ex is TypeInitializationException || ex is System.Configuration.ConfigurationErrorsException)
                {
                    MessageBox.Show($"خطأ حرج في بدء التطبيق: {ex.Message}", "خطأ في البدء",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    Application.Current.Shutdown();
                }
            }
        }

        private void InitializeModernFeatures()
        {
            try
            {
                // Add entrance animation
                PlayEntranceAnimation();
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"فشل في تهيئة الميزات الحديثة: {ex.Message}");
            }
        }

        private void InitializeNotificationContainers()
        {
            try
            {
                // تهيئة حاوي Toast
                if (Content is Grid mainGrid)
                {
                    _toastService?.SetContainer(mainGrid);
                    _successNotificationService?.SetContainer(mainGrid);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تهيئة حاوي الإشعارات: {ex.Message}");
            }
        }

        private void MainWindow_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                // تحسين عرض النافذة لتملأ الشاشة مع ترك مساحة لشريط المهام
                MaximizeWindowWithTaskbar();

                // تهيئة حاوي الإشعارات
                InitializeNotificationContainers();

                // التنقل إلى لوحة التحكم
                _navigationService.NavigateTo(typeof(DashboardPage), "");
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", $"فشل في تحميل التطبيق: {ex.Message}");
            }
        }

        private void OnNavigated(object? sender, NavigationEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"OnNavigated: Received navigation event for {e.Title}");
                System.Console.WriteLine($"MainWindow: OnNavigated called for {e.Title}");

                // Ensure UI updates happen on the UI thread
                Dispatcher.Invoke(() =>
                {
                    System.Diagnostics.Debug.WriteLine($"OnNavigated: Starting UI update for {e.Title}");
                    System.Console.WriteLine($"MainWindow: Starting UI update for {e.Title}");

                    // Find elements by name since they might not be auto-generated
                    var pageTitleText = FindName("PageTitleText") as TextBlock;
                    var backButton = FindName("BackButton") as Button;
                    var mainContentPresenter = FindName("MainContentPresenter") as ContentPresenter;

                    System.Diagnostics.Debug.WriteLine($"OnNavigated: Found elements - Title: {pageTitleText != null}, Back: {backButton != null}, Content: {mainContentPresenter != null}");

                    // Update page title
                    if (pageTitleText != null)
                    {
                        pageTitleText.Text = e.Title;
                        System.Diagnostics.Debug.WriteLine($"OnNavigated: Updated page title to {e.Title}");
                    }

                    // Update back button visibility
                    if (backButton != null)
                    {
                        backButton.Visibility = _navigationService.CanGoBack ? Visibility.Visible : Visibility.Collapsed;
                    }

                    // Set page content directly
                    if (mainContentPresenter != null)
                    {
                        mainContentPresenter.Content = e.Page;
                        System.Diagnostics.Debug.WriteLine($"OnNavigated: Set content to {e.Page?.GetType().Name}");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("OnNavigated: MainContentPresenter is null!");
                    }

                    System.Diagnostics.Debug.WriteLine($"OnNavigated: UI update completed for {e.Title}");
                });
            }
            catch (Exception ex)
            {
                Dispatcher.Invoke(() =>
                {
                    System.Diagnostics.Debug.WriteLine($"خطأ في التنقل: {ex.Message}");

                    // Use toast service if available, otherwise log only
                    try
                    {
                        _toastService?.ShowError("خطأ في التنقل", ex.Message);
                    }
                    catch
                    {
                        // Silently fail to avoid cascading errors
                        System.Diagnostics.Debug.WriteLine("Failed to show navigation error toast");
                    }
                });
            }
        }



        private void BackButton_Click(object sender, RoutedEventArgs e)
        {
            if (_navigationService.CanGoBack)
            {
                _navigationService.GoBack();
            }
        }

        private void HomeButton_Click(object sender, RoutedEventArgs e)
        {
            _navigationService.NavigateTo(typeof(DashboardPage), "");
        }

        // Window Control Handlers
        private void HeaderBar_MouseLeftButtonDown(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            if (e.ClickCount == 2)
            {
                // Double click to maximize/restore
                MaximizeButton_Click(sender, new RoutedEventArgs());
            }
            else
            {
                // Single click to drag
                DragMove();
            }
        }

        private void MinimizeButton_Click(object sender, RoutedEventArgs e)
        {
            WindowState = WindowState.Minimized;
        }

        private void MaximizeButton_Click(object sender, RoutedEventArgs e)
        {
            var maximizeButton = FindName("MaximizeButton") as Button;

            // تحقق من الحالة الحالية للنافذة
            var isCurrentlyMaximized = (Width >= SystemParameters.WorkArea.Width * 0.95 &&
                                      Height >= SystemParameters.WorkArea.Height * 0.95);

            if (isCurrentlyMaximized)
            {
                // إعادة النافذة إلى الحجم العادي
                CenterWindowOnScreen();
                if (maximizeButton != null)
                    maximizeButton.Content = "🗖";
            }
            else
            {
                // تكبير النافذة مع ترك مساحة لشريط المهام
                MaximizeWindowWithTaskbar();
                if (maximizeButton != null)
                    maximizeButton.Content = "🗗";
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        // Navigation Button Handler
        private void NavigationButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button)
                {
                    // Reset all buttons to normal style
                    ResetNavigationButtonStyles();

                    // Set clicked button to selected style
                    button.Style = (Style)FindResource("SelectedNavButtonStyle");

                    // Navigate based on button name
                    switch (button.Name)
                    {
                        case "DashboardButton":
                            System.Console.WriteLine("MainWindow: Navigating to DashboardPage");
                            _navigationService.NavigateTo(typeof(DashboardPage), "");
                            break;
                        case "InvoicesButton":
                            System.Console.WriteLine("MainWindow: Navigating to InvoicesPage");
                            _navigationService.NavigateTo(typeof(InvoicesPage), "");
                            break;
                        case "PaymentsButton":
                            System.Console.WriteLine("MainWindow: Navigating to PaymentsPage");
                            _navigationService.NavigateTo(typeof(PaymentsPage), "");
                            break;
                        case "SuppliersButton":
                            System.Console.WriteLine("MainWindow: Navigating to SuppliersPage");
                            _navigationService.NavigateTo(typeof(SuppliersPage), "");
                            break;
                        case "SearchButton":
                            System.Console.WriteLine("MainWindow: Navigating to SearchPage");
                            _navigationService.NavigateTo(typeof(SearchPage), "");
                            break;
                        case "ReportsButton":
                            System.Diagnostics.Debug.WriteLine("ReportsButton clicked - attempting navigation");
                            System.Console.WriteLine("MainWindow: Navigating to ReportsPage");
                            _toastService?.ShowInfo("تحميل", "جاري تحميل صفحة التقارير...");
                            _navigationService.NavigateTo(typeof(ReportsPage), "");
                            break;
                        case "SettingsButton":
                            System.Console.WriteLine("MainWindow: Navigating to SettingsPage");
                            _toastService?.ShowInfo("تحميل", "جاري تحميل صفحة الإعدادات...");
                            _navigationService.NavigateTo(typeof(SettingsPage), "");
                            break;
                        case "ImportExportButton":
                            System.Console.WriteLine("MainWindow: Navigating to ImportExportPage");
                            _toastService?.ShowInfo("تحميل", "جاري تحميل صفحة الاستيراد والتصدير...");
                            _navigationService.NavigateTo(typeof(ImportExportPage), "");
                            break;
                        case "BackupRestoreButton":
                            System.Console.WriteLine("MainWindow: Navigating to BackupRestorePage");
                            _toastService?.ShowInfo("تحميل", "جاري تحميل صفحة النسخ الاحتياطي...");
                            _navigationService.NavigateTo(typeof(BackupRestorePage), "");
                            break;
                        case "PerformanceButton":
                            System.Console.WriteLine("MainWindow: Navigating to PerformanceOptimizationPage");
                            _toastService?.ShowInfo("تحميل", "جاري تحميل صفحة تحسين الأداء...");
                            _navigationService.NavigateTo(typeof(PerformanceOptimizationPage), "");
                            break;
                        case "OffersButton":
                            System.Console.WriteLine("MainWindow: Navigating to OffersPage");
                            _navigationService.NavigateTo(typeof(OffersPage), "");
                            break;
                    }
                }
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"فشل في التنقل: {ex.Message}");
            }
        }

        private void ResetNavigationButtonStyles()
        {
            var normalStyle = (Style)FindResource("ModernNavButtonStyle");

            var dashboardButton = FindName("DashboardButton") as Button;
            var invoicesButton = FindName("InvoicesButton") as Button;
            var paymentsButton = FindName("PaymentsButton") as Button;
            var suppliersButton = FindName("SuppliersButton") as Button;
            var searchButton = FindName("SearchButton") as Button;
            var reportsButton = FindName("ReportsButton") as Button;
            var settingsButton = FindName("SettingsButton") as Button;
            var importExportButton = FindName("ImportExportButton") as Button;
            var backupRestoreButton = FindName("BackupRestoreButton") as Button;
            var performanceButton = FindName("PerformanceButton") as Button;
            var offersButton = FindName("OffersButton") as Button;

            if (dashboardButton != null) dashboardButton.Style = normalStyle;
            if (invoicesButton != null) invoicesButton.Style = normalStyle;
            if (paymentsButton != null) paymentsButton.Style = normalStyle;
            if (suppliersButton != null) suppliersButton.Style = normalStyle;
            if (searchButton != null) searchButton.Style = normalStyle;
            if (reportsButton != null) reportsButton.Style = normalStyle;
            if (settingsButton != null) settingsButton.Style = normalStyle;
            if (importExportButton != null) importExportButton.Style = normalStyle;
            if (backupRestoreButton != null) backupRestoreButton.Style = normalStyle;
            if (performanceButton != null) performanceButton.Style = normalStyle;
            if (offersButton != null) offersButton.Style = normalStyle;
        }







        private void PlayEntranceAnimation()
        {
            try
            {
                // Simple opacity animation for entrance
                var opacityAnimation = new DoubleAnimation
                {
                    From = 0,
                    To = 1,
                    Duration = TimeSpan.FromMilliseconds(400)
                };

                BeginAnimation(OpacityProperty, opacityAnimation);
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"فشل في تشغيل الرسوم المتحركة: {ex.Message}");
            }
        }

        // Enhanced Page Transition Animation
        private void AnimatePageTransition(UserControl newPage)
        {
            try
            {
                var mainContentPresenter = FindName("MainContentPresenter") as ContentPresenter;
                if (mainContentPresenter == null) return;

                if (mainContentPresenter.Content != null)
                {
                    // Simple fade transition
                    var fadeOutAnimation = new DoubleAnimation
                    {
                        From = 1,
                        To = 0,
                        Duration = TimeSpan.FromMilliseconds(200)
                    };

                    fadeOutAnimation.Completed += (s, e) =>
                    {
                        // Set new content
                        mainContentPresenter.Content = newPage;

                        // Animate in new page
                        var fadeInAnimation = new DoubleAnimation
                        {
                            From = 0,
                            To = 1,
                            Duration = TimeSpan.FromMilliseconds(300)
                        };

                        mainContentPresenter.BeginAnimation(OpacityProperty, fadeInAnimation);
                    };

                    mainContentPresenter.BeginAnimation(OpacityProperty, fadeOutAnimation);
                }
                else
                {
                    // First time loading
                    mainContentPresenter.Content = newPage;

                    var fadeInAnimation = new DoubleAnimation
                    {
                        From = 0,
                        To = 1,
                        Duration = TimeSpan.FromMilliseconds(400)
                    };

                    mainContentPresenter.BeginAnimation(OpacityProperty, fadeInAnimation);
                }
            }
            catch (Exception ex)
            {
                // Fallback to simple content change
                var mainContentPresenter = FindName("MainContentPresenter") as ContentPresenter;
                if (mainContentPresenter != null)
                    mainContentPresenter.Content = newPage;
                _toastService?.ShowError("خطأ", $"فشل في تشغيل انتقال الصفحة: {ex.Message}");
            }
        }

        // Settings and Notifications handlers
        private void SettingsButton_Click(object sender, RoutedEventArgs e)
        {
            _toastService.ShowInfo("قريباً", "صفحة الإعدادات قيد التطوير");
        }

        private void NotificationsButton_Click(object sender, RoutedEventArgs e)
        {
            _toastService.ShowInfo("التنبيهات", "لديك 3 تنبيهات جديدة");
            // You can implement a notifications panel here
        }





        /// <summary>
        /// تحسين عرض النافذة في وسط الشاشة
        /// </summary>
        private void CenterWindowOnScreen()
        {
            // التأكد من أن النافذة تظهر في وسط الشاشة
            WindowStartupLocation = WindowStartupLocation.CenterScreen;

            // تحديد الحد الأدنى والأقصى للحجم
            MinWidth = 1000;
            MinHeight = 700;

            // تحسين الحجم بناءً على دقة الشاشة
            var screenWidth = SystemParameters.PrimaryScreenWidth;
            var screenHeight = SystemParameters.PrimaryScreenHeight;

            // تعيين حجم النافذة بنسبة مناسبة من الشاشة
            if (screenWidth >= 1920 && screenHeight >= 1080)
            {
                Width = 1400;
                Height = 900;
            }
            else if (screenWidth >= 1366 && screenHeight >= 768)
            {
                Width = 1200;
                Height = 800;
            }
            else
            {
                Width = 1000;
                Height = 700;
            }

            // التأكد من أن النافذة لا تتجاوز حدود الشاشة
            if (Width > screenWidth * 0.9)
                Width = screenWidth * 0.9;
            if (Height > screenHeight * 0.9)
                Height = screenHeight * 0.9;
        }

        private void MaximizeWindowWithTaskbar()
        {
            try
            {
                // الحصول على أبعاد منطقة العمل (بدون شريط المهام)
                var workingArea = SystemParameters.WorkArea;

                // ضبط موقع وحجم النافذة لتملأ منطقة العمل
                WindowState = WindowState.Normal; // إعادة تعيين الحالة أولاً
                Left = workingArea.Left;
                Top = workingArea.Top;
                Width = workingArea.Width;
                Height = workingArea.Height;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تكبير النافذة: {ex.Message}");
                // في حالة الفشل، استخدم الطريقة التقليدية
                CenterWindowOnScreen();
            }
        }

        /// <summary>
        /// فتح نافذة إعدادات التخزين السحابي
        /// </summary>
        private void CloudStorageButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var cloudService = App.ServiceProvider.GetRequiredService<ICloudStorageService>();
                var cloudAuthWindow = new Windows.CloudAuthWindow(cloudService, _toastService)
                {
                    Owner = this,
                    WindowStartupLocation = WindowStartupLocation.CenterOwner
                };

                cloudAuthWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"فشل في فتح نافذة التخزين السحابي: {ex.Message}");
            }
        }
    }
}