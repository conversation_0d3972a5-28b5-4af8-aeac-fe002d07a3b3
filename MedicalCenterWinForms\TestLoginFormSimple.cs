using System;
using System.Windows.Forms;
using MedicalCenterWinForms.Forms;
using MedicalCenterWinForms.Services;

namespace MedicalCenterWinForms
{
    public static class TestLoginFormSimple
    {
        [STAThread]
        public static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            try
            {
                // Create database service
                var databaseService = new DatabaseService();
                
                // Create and show login form
                using (var loginForm = new LoginForm(databaseService))
                {
                    loginForm.WindowState = FormWindowState.Normal;
                    loginForm.StartPosition = FormStartPosition.CenterScreen;
                    loginForm.TopMost = true;
                    
                    var result = loginForm.ShowDialog();
                    
                    if (result == DialogResult.OK)
                    {
                        MessageBox.Show($"تم تسجيل الدخول بنجاح!\nالمستخدم: {loginForm.CurrentUser?.Username}", 
                                      "نجح تسجيل الدخول", 
                                      MessageBoxButtons.OK, 
                                      MessageBoxIcon.Information);
                    }
                    else
                    {
                        MessageBox.Show("تم إلغاء تسجيل الدخول", 
                                      "إلغاء", 
                                      MessageBoxButtons.OK, 
                                      MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}\n\nتفاصيل الخطأ:\n{ex.StackTrace}", 
                              "خطأ", 
                              MessageBoxButtons.OK, 
                              MessageBoxIcon.Error);
            }
        }
    }
}
