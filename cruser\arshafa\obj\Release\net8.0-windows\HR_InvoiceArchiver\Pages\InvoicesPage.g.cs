﻿#pragma checksum "..\..\..\..\..\HR_InvoiceArchiver\Pages\InvoicesPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "EF89F9669D30DF1D207B151AB51BB4A315EBD953"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using HR_InvoiceArchiver.Controls;
using HR_InvoiceArchiver.Converters;
using LiveCharts.Wpf;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace HR_InvoiceArchiver.Pages {
    
    
    /// <summary>
    /// InvoicesPage
    /// </summary>
    public partial class InvoicesPage : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 245 "..\..\..\..\..\HR_InvoiceArchiver\Pages\InvoicesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid MainContentGrid;
        
        #line default
        #line hidden
        
        
        #line 296 "..\..\..\..\..\HR_InvoiceArchiver\Pages\InvoicesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportButton;
        
        #line default
        #line hidden
        
        
        #line 308 "..\..\..\..\..\HR_InvoiceArchiver\Pages\InvoicesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PrintButton;
        
        #line default
        #line hidden
        
        
        #line 321 "..\..\..\..\..\HR_InvoiceArchiver\Pages\InvoicesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddInvoiceButton;
        
        #line default
        #line hidden
        
        
        #line 361 "..\..\..\..\..\HR_InvoiceArchiver\Pages\InvoicesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalInvoicesText;
        
        #line default
        #line hidden
        
        
        #line 382 "..\..\..\..\..\HR_InvoiceArchiver\Pages\InvoicesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock UnpaidInvoicesText;
        
        #line default
        #line hidden
        
        
        #line 403 "..\..\..\..\..\HR_InvoiceArchiver\Pages\InvoicesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PartiallyPaidInvoicesText;
        
        #line default
        #line hidden
        
        
        #line 424 "..\..\..\..\..\HR_InvoiceArchiver\Pages\InvoicesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PaidInvoicesText;
        
        #line default
        #line hidden
        
        
        #line 445 "..\..\..\..\..\HR_InvoiceArchiver\Pages\InvoicesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalAmountText;
        
        #line default
        #line hidden
        
        
        #line 486 "..\..\..\..\..\HR_InvoiceArchiver\Pages\InvoicesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 495 "..\..\..\..\..\HR_InvoiceArchiver\Pages\InvoicesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button FilterButton;
        
        #line default
        #line hidden
        
        
        #line 509 "..\..\..\..\..\HR_InvoiceArchiver\Pages\InvoicesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshButton;
        
        #line default
        #line hidden
        
        
        #line 521 "..\..\..\..\..\HR_InvoiceArchiver\Pages\InvoicesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button QuickFilterUnpaidButton;
        
        #line default
        #line hidden
        
        
        #line 530 "..\..\..\..\..\HR_InvoiceArchiver\Pages\InvoicesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button QuickFilterPaidButton;
        
        #line default
        #line hidden
        
        
        #line 542 "..\..\..\..\..\HR_InvoiceArchiver\Pages\InvoicesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border FilterPanel;
        
        #line default
        #line hidden
        
        
        #line 559 "..\..\..\..\..\HR_InvoiceArchiver\Pages\InvoicesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox StatusFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 572 "..\..\..\..\..\HR_InvoiceArchiver\Pages\InvoicesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker FromDatePicker;
        
        #line default
        #line hidden
        
        
        #line 579 "..\..\..\..\..\HR_InvoiceArchiver\Pages\InvoicesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker ToDatePicker;
        
        #line default
        #line hidden
        
        
        #line 586 "..\..\..\..\..\HR_InvoiceArchiver\Pages\InvoicesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearFiltersButton;
        
        #line default
        #line hidden
        
        
        #line 606 "..\..\..\..\..\HR_InvoiceArchiver\Pages\InvoicesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid InvoicesDataGrid;
        
        #line default
        #line hidden
        
        
        #line 947 "..\..\..\..\..\HR_InvoiceArchiver\Pages\InvoicesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border LoadingPanel;
        
        #line default
        #line hidden
        
        
        #line 964 "..\..\..\..\..\HR_InvoiceArchiver\Pages\InvoicesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border EmptyStatePanel;
        
        #line default
        #line hidden
        
        
        #line 993 "..\..\..\..\..\HR_InvoiceArchiver\Pages\InvoicesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HR_InvoiceArchiver.Controls.InvoiceFormOverlay InvoiceFormOverlay;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/TestApp;component/hr_invoicearchiver/pages/invoicespage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\HR_InvoiceArchiver\Pages\InvoicesPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal System.Delegate _CreateDelegate(System.Type delegateType, string handler) {
            return System.Delegate.CreateDelegate(delegateType, this, handler);
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.MainContentGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 2:
            this.ExportButton = ((System.Windows.Controls.Button)(target));
            
            #line 298 "..\..\..\..\..\HR_InvoiceArchiver\Pages\InvoicesPage.xaml"
            this.ExportButton.Click += new System.Windows.RoutedEventHandler(this.ExportButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.PrintButton = ((System.Windows.Controls.Button)(target));
            
            #line 310 "..\..\..\..\..\HR_InvoiceArchiver\Pages\InvoicesPage.xaml"
            this.PrintButton.Click += new System.Windows.RoutedEventHandler(this.PrintButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.AddInvoiceButton = ((System.Windows.Controls.Button)(target));
            
            #line 323 "..\..\..\..\..\HR_InvoiceArchiver\Pages\InvoicesPage.xaml"
            this.AddInvoiceButton.Click += new System.Windows.RoutedEventHandler(this.AddInvoiceButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.TotalInvoicesText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.UnpaidInvoicesText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.PartiallyPaidInvoicesText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.PaidInvoicesText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.TotalAmountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.SearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 488 "..\..\..\..\..\HR_InvoiceArchiver\Pages\InvoicesPage.xaml"
            this.SearchTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.SearchTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 11:
            this.FilterButton = ((System.Windows.Controls.Button)(target));
            
            #line 497 "..\..\..\..\..\HR_InvoiceArchiver\Pages\InvoicesPage.xaml"
            this.FilterButton.Click += new System.Windows.RoutedEventHandler(this.FilterButton_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.RefreshButton = ((System.Windows.Controls.Button)(target));
            
            #line 511 "..\..\..\..\..\HR_InvoiceArchiver\Pages\InvoicesPage.xaml"
            this.RefreshButton.Click += new System.Windows.RoutedEventHandler(this.RefreshButton_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.QuickFilterUnpaidButton = ((System.Windows.Controls.Button)(target));
            
            #line 523 "..\..\..\..\..\HR_InvoiceArchiver\Pages\InvoicesPage.xaml"
            this.QuickFilterUnpaidButton.Click += new System.Windows.RoutedEventHandler(this.QuickFilterUnpaid_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.QuickFilterPaidButton = ((System.Windows.Controls.Button)(target));
            
            #line 532 "..\..\..\..\..\HR_InvoiceArchiver\Pages\InvoicesPage.xaml"
            this.QuickFilterPaidButton.Click += new System.Windows.RoutedEventHandler(this.QuickFilterPaid_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.FilterPanel = ((System.Windows.Controls.Border)(target));
            return;
            case 16:
            this.StatusFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 561 "..\..\..\..\..\HR_InvoiceArchiver\Pages\InvoicesPage.xaml"
            this.StatusFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.StatusFilterComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 17:
            this.FromDatePicker = ((System.Windows.Controls.DatePicker)(target));
            
            #line 574 "..\..\..\..\..\HR_InvoiceArchiver\Pages\InvoicesPage.xaml"
            this.FromDatePicker.SelectedDateChanged += new System.EventHandler<System.Windows.Controls.SelectionChangedEventArgs>(this.DateFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 18:
            this.ToDatePicker = ((System.Windows.Controls.DatePicker)(target));
            
            #line 581 "..\..\..\..\..\HR_InvoiceArchiver\Pages\InvoicesPage.xaml"
            this.ToDatePicker.SelectedDateChanged += new System.EventHandler<System.Windows.Controls.SelectionChangedEventArgs>(this.DateFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 19:
            this.ClearFiltersButton = ((System.Windows.Controls.Button)(target));
            
            #line 588 "..\..\..\..\..\HR_InvoiceArchiver\Pages\InvoicesPage.xaml"
            this.ClearFiltersButton.Click += new System.Windows.RoutedEventHandler(this.ClearFiltersButton_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            this.InvoicesDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 619 "..\..\..\..\..\HR_InvoiceArchiver\Pages\InvoicesPage.xaml"
            this.InvoicesDataGrid.MouseDoubleClick += new System.Windows.Input.MouseButtonEventHandler(this.InvoicesDataGrid_MouseDoubleClick);
            
            #line default
            #line hidden
            return;
            case 23:
            this.LoadingPanel = ((System.Windows.Controls.Border)(target));
            return;
            case 24:
            this.EmptyStatePanel = ((System.Windows.Controls.Border)(target));
            return;
            case 25:
            
            #line 980 "..\..\..\..\..\HR_InvoiceArchiver\Pages\InvoicesPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AddInvoiceButton_Click);
            
            #line default
            #line hidden
            return;
            case 26:
            this.InvoiceFormOverlay = ((HR_InvoiceArchiver.Controls.InvoiceFormOverlay)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 21:
            
            #line 875 "..\..\..\..\..\HR_InvoiceArchiver\Pages\InvoicesPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewInvoiceAttachment_Click);
            
            #line default
            #line hidden
            break;
            case 22:
            
            #line 899 "..\..\..\..\..\HR_InvoiceArchiver\Pages\InvoicesPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewReceiptAttachment_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

