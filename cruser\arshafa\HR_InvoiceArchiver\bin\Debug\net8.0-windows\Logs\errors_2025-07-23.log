﻿[2025-07-23 00:03:04] INFO: Global Exception Handler initialized successfully
[2025-07-23 00:38:37] INFO: Global Exception Handler initialized successfully
[2025-07-23 00:40:59] INFO: Global Exception Handler initialized successfully
[2025-07-23 00:41:07] ERROR in UI Thread
Exception Type: XamlParseException
Message: 'Set property 'MaterialDesignThemes.Wpf.HintAssist.HelperText' threw an exception.' Line number '152' and line position '38'.
Stack Trace:    at System.Windows.Markup.XamlReader.RewrapException(Exception e, IXamlLineInfo lineInfo, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.Load(XamlReader xamlReader, IXamlObjectWriterFactory writerFactory, Boolean skipJournaledProperties, Object rootObject, XamlObjectWriterSettings settings, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.LoadBaml(XamlReader xaml<PERSON><PERSON>er, Boolean skipJournaledProperties, Object rootObject, XamlAccessLevel accessLevel, Uri baseUri)
   at System.Windows.Markup.XamlReader.LoadBaml(Stream stream, ParserContext parserContext, Object parent, Boolean closeStream)
   at System.Windows.Application.LoadComponent(Object component, Uri resourceLocator)
   at HR_InvoiceArchiver.Controls.OfferFormWindow.InitializeComponent() in C:\Users\<USER>\Desktop\ahmed\new\cruser\arshafa\HR_InvoiceArchiver\Controls\OfferFormWindow.xaml:line 1
   at HR_InvoiceArchiver.Controls.OfferFormWindow..ctor() in C:\Users\<USER>\Desktop\ahmed\new\cruser\arshafa\HR_InvoiceArchiver\Controls\OfferFormWindow.xaml.cs:line 24
   at HR_InvoiceArchiver.Controls.OfferFormOverlay.ShowAddOfferForm() in C:\Users\<USER>\Desktop\ahmed\new\cruser\arshafa\HR_InvoiceArchiver\Controls\OfferFormOverlay.xaml.cs:line 24
   at HR_InvoiceArchiver.Pages.OffersPage.AddOfferButton_Click(Object sender, RoutedEventArgs e) in C:\Users\<USER>\Desktop\ahmed\new\cruser\arshafa\HR_InvoiceArchiver\Pages\OffersPage.xaml.cs:line 232
   at System.Windows.EventRoute.InvokeHandlersImpl(Object source, RoutedEventArgs args, Boolean reRaised)
   at System.Windows.UIElement.RaiseEventImpl(DependencyObject sender, RoutedEventArgs args)
   at System.Windows.Controls.Primitives.ButtonBase.OnClick()
   at System.Windows.Controls.Button.OnClick()
   at System.Windows.Controls.Primitives.ButtonBase.OnMouseLeftButtonUp(MouseButtonEventArgs e)
   at System.Windows.RoutedEventArgs.InvokeHandler(Delegate handler, Object target)
   at System.Windows.EventRoute.InvokeHandlersImpl(Object source, RoutedEventArgs args, Boolean reRaised)
   at System.Windows.UIElement.ReRaiseEventAs(DependencyObject sender, RoutedEventArgs args, RoutedEvent newEvent)
   at System.Windows.RoutedEventArgs.InvokeHandler(Delegate handler, Object target)
   at System.Windows.EventRoute.InvokeHandlersImpl(Object source, RoutedEventArgs args, Boolean reRaised)
   at System.Windows.UIElement.RaiseEventImpl(DependencyObject sender, RoutedEventArgs args)
   at System.Windows.UIElement.RaiseTrustedEvent(RoutedEventArgs args)
   at System.Windows.Input.InputManager.ProcessStagingArea()
   at System.Windows.Input.InputProviderSite.ReportInput(InputReport inputReport)
   at System.Windows.Interop.HwndMouseInputProvider.ReportInput(IntPtr hwnd, InputMode mode, Int32 timestamp, RawMouseActions actions, Int32 x, Int32 y, Int32 wheel)
   at System.Windows.Interop.HwndMouseInputProvider.FilterMessage(IntPtr hwnd, WindowMessage msg, IntPtr wParam, IntPtr lParam, Boolean& handled)
   at System.Windows.Interop.HwndSource.InputFilterMessage(IntPtr hwnd, Int32 msg, IntPtr wParam, IntPtr lParam, Boolean& handled)
   at MS.Win32.HwndWrapper.WndProc(IntPtr hwnd, Int32 msg, IntPtr wParam, IntPtr lParam, Boolean& handled)
   at MS.Win32.HwndSubclass.DispatcherCallbackOperation(Object o)
   at System.Windows.Threading.ExceptionWrapper.InternalRealCall(Delegate callback, Object args, Int32 numArgs)
   at System.Windows.Threading.ExceptionWrapper.TryCatchWhen(Object source, Delegate callback, Object args, Int32 numArgs, Delegate catchHandler)
Inner Exception:
  Type: ArgumentException
  Message: 'System.Windows.Controls.TextBlock' is not a valid value for property 'HelperText'.
--------------------------------------------------------------------------------
[2025-07-23 00:41:11] ERROR in UI Thread
Exception Type: XamlParseException
Message: 'Set property 'MaterialDesignThemes.Wpf.HintAssist.HelperText' threw an exception.' Line number '152' and line position '38'.
Stack Trace:    at System.Windows.Markup.XamlReader.RewrapException(Exception e, IXamlLineInfo lineInfo, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.Load(XamlReader xamlReader, IXamlObjectWriterFactory writerFactory, Boolean skipJournaledProperties, Object rootObject, XamlObjectWriterSettings settings, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.LoadBaml(XamlReader xamlReader, Boolean skipJournaledProperties, Object rootObject, XamlAccessLevel accessLevel, Uri baseUri)
   at System.Windows.Markup.XamlReader.LoadBaml(Stream stream, ParserContext parserContext, Object parent, Boolean closeStream)
   at System.Windows.Application.LoadComponent(Object component, Uri resourceLocator)
   at HR_InvoiceArchiver.Controls.OfferFormWindow.InitializeComponent() in C:\Users\<USER>\Desktop\ahmed\new\cruser\arshafa\HR_InvoiceArchiver\Controls\OfferFormWindow.xaml:line 1
   at HR_InvoiceArchiver.Controls.OfferFormWindow..ctor() in C:\Users\<USER>\Desktop\ahmed\new\cruser\arshafa\HR_InvoiceArchiver\Controls\OfferFormWindow.xaml.cs:line 24
   at HR_InvoiceArchiver.Controls.OfferFormOverlay.ShowAddOfferForm() in C:\Users\<USER>\Desktop\ahmed\new\cruser\arshafa\HR_InvoiceArchiver\Controls\OfferFormOverlay.xaml.cs:line 24
   at HR_InvoiceArchiver.Pages.OffersPage.AddOfferButton_Click(Object sender, RoutedEventArgs e) in C:\Users\<USER>\Desktop\ahmed\new\cruser\arshafa\HR_InvoiceArchiver\Pages\OffersPage.xaml.cs:line 232
   at System.Windows.EventRoute.InvokeHandlersImpl(Object source, RoutedEventArgs args, Boolean reRaised)
   at System.Windows.UIElement.RaiseEventImpl(DependencyObject sender, RoutedEventArgs args)
   at System.Windows.Controls.Primitives.ButtonBase.OnClick()
   at System.Windows.Controls.Button.OnClick()
   at System.Windows.Controls.Primitives.ButtonBase.OnMouseLeftButtonUp(MouseButtonEventArgs e)
   at System.Windows.RoutedEventArgs.InvokeHandler(Delegate handler, Object target)
   at System.Windows.EventRoute.InvokeHandlersImpl(Object source, RoutedEventArgs args, Boolean reRaised)
   at System.Windows.UIElement.ReRaiseEventAs(DependencyObject sender, RoutedEventArgs args, RoutedEvent newEvent)
   at System.Windows.RoutedEventArgs.InvokeHandler(Delegate handler, Object target)
   at System.Windows.EventRoute.InvokeHandlersImpl(Object source, RoutedEventArgs args, Boolean reRaised)
   at System.Windows.UIElement.RaiseEventImpl(DependencyObject sender, RoutedEventArgs args)
   at System.Windows.UIElement.RaiseTrustedEvent(RoutedEventArgs args)
   at System.Windows.Input.InputManager.ProcessStagingArea()
   at System.Windows.Interop.HwndMouseInputProvider.ReportInput(IntPtr hwnd, InputMode mode, Int32 timestamp, RawMouseActions actions, Int32 x, Int32 y, Int32 wheel)
   at System.Windows.Interop.HwndMouseInputProvider.FilterMessage(IntPtr hwnd, WindowMessage msg, IntPtr wParam, IntPtr lParam, Boolean& handled)
   at System.Windows.Interop.HwndSource.InputFilterMessage(IntPtr hwnd, Int32 msg, IntPtr wParam, IntPtr lParam, Boolean& handled)
   at MS.Win32.HwndWrapper.WndProc(IntPtr hwnd, Int32 msg, IntPtr wParam, IntPtr lParam, Boolean& handled)
   at System.Windows.Threading.ExceptionWrapper.InternalRealCall(Delegate callback, Object args, Int32 numArgs)
   at System.Windows.Threading.ExceptionWrapper.TryCatchWhen(Object source, Delegate callback, Object args, Int32 numArgs, Delegate catchHandler)
Inner Exception:
  Type: ArgumentException
  Message: 'System.Windows.Controls.TextBlock' is not a valid value for property 'HelperText'.
--------------------------------------------------------------------------------
[2025-07-23 00:49:41] INFO: Global Exception Handler initialized successfully
[2025-07-23 00:51:21] INFO: Global Exception Handler initialized successfully
[2025-07-23 00:53:23] INFO: Global Exception Handler initialized successfully
