<UserControl x:Class="HR_InvoiceArchiver.Pages.OffersPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:controls="clr-namespace:HR_InvoiceArchiver.Controls"
             FlowDirection="RightToLeft"
             Background="#F5F7FA">

    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/HR_InvoiceArchiver;component/Styles/MaterialDesignStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>

            <!-- Converters -->
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>

            <!-- Modern Color Palette -->
            <SolidColorBrush x:Key="PrimaryBlue">#2196F3</SolidColorBrush>
            <SolidColorBrush x:Key="PrimaryDark">#1976D2</SolidColorBrush>
            <SolidColorBrush x:Key="AccentGreen">#4CAF50</SolidColorBrush>
            <SolidColorBrush x:Key="AccentOrange">#FF9800</SolidColorBrush>
            <SolidColorBrush x:Key="AccentRed">#F44336</SolidColorBrush>
            <SolidColorBrush x:Key="TextPrimary">#212121</SolidColorBrush>
            <SolidColorBrush x:Key="TextSecondary">#757575</SolidColorBrush>
            <SolidColorBrush x:Key="SurfaceColor">#FFFFFF</SolidColorBrush>
            <SolidColorBrush x:Key="BackgroundLight">#FAFBFC</SolidColorBrush>

            <!-- Modern Card Style -->
            <Style x:Key="ModernOfferCardStyle" TargetType="materialDesign:Card">
                <Setter Property="Background" Value="{StaticResource SurfaceColor}"/>
                <Setter Property="Padding" Value="24"/>
                <Setter Property="Margin" Value="12"/>
                <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth2"/>
                <Setter Property="CornerRadius" Value="12"/>
                <Setter Property="Effect">
                    <Setter.Value>
                        <DropShadowEffect Color="#40000000" BlurRadius="12" ShadowDepth="4" Opacity="0.15"/>
                    </Setter.Value>
                </Setter>
            </Style>

            <!-- Modern Button Styles -->
            <Style x:Key="PrimaryActionButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
                <Setter Property="Background" Value="{StaticResource PrimaryBlue}"/>
                <Setter Property="Foreground" Value="White"/>
                <Setter Property="Height" Value="44"/>
                <Setter Property="FontWeight" Value="Medium"/>
                <Setter Property="Padding" Value="20,0"/>
                <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth1"/>
            </Style>

            <Style x:Key="SecondaryActionButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignOutlinedButton}">
                <Setter Property="BorderBrush" Value="{StaticResource PrimaryBlue}"/>
                <Setter Property="Foreground" Value="{StaticResource PrimaryBlue}"/>
                <Setter Property="Height" Value="44"/>
                <Setter Property="FontWeight" Value="Medium"/>
                <Setter Property="Padding" Value="20,0"/>
            </Style>
        </ResourceDictionary>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Modern Header with Gradient -->
        <Border Grid.Row="0" Margin="20,20,20,0">
            <Border.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                    <GradientStop Color="#2196F3" Offset="0"/>
                    <GradientStop Color="#1976D2" Offset="1"/>
                </LinearGradientBrush>
            </Border.Background>
            <Border.Effect>
                <DropShadowEffect Color="#40000000" BlurRadius="16" ShadowDepth="6" Opacity="0.2"/>
            </Border.Effect>
            <Border CornerRadius="16" Padding="32,24">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- Icon and Title -->
                    <StackPanel Grid.Column="0" Orientation="Horizontal">
                        <Border Background="rgba(255,255,255,0.2)" CornerRadius="12" Padding="12" Margin="0,0,20,0">
                            <materialDesign:PackIcon Kind="Handshake" Width="32" Height="32" Foreground="White"/>
                        </Border>
                        <StackPanel VerticalAlignment="Center">
                            <TextBlock Text="عروض المندوبين" FontSize="28" FontWeight="Bold" Foreground="White"/>
                            <TextBlock Text="إدارة وتحليل عروض المندوبين لاختيار أفضل الصفقات"
                                     FontSize="16" Foreground="#E3F2FD" Margin="0,4,0,0"/>
                        </StackPanel>
                    </StackPanel>

                    <!-- Quick Stats -->
                    <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center">
                        <Border Background="rgba(255,255,255,0.15)" CornerRadius="8" Padding="16,8" Margin="0,0,12,0">
                            <StackPanel>
                                <TextBlock x:Name="TotalOffersText" Text="0" FontSize="20" FontWeight="Bold"
                                         Foreground="White" HorizontalAlignment="Center"/>
                                <TextBlock Text="إجمالي العروض" FontSize="12" Foreground="#E3F2FD"
                                         HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Border>
                        <Border Background="rgba(255,255,255,0.15)" CornerRadius="8" Padding="16,8">
                            <StackPanel>
                                <TextBlock x:Name="BestOffersText" Text="0" FontSize="20" FontWeight="Bold"
                                         Foreground="White" HorizontalAlignment="Center"/>
                                <TextBlock Text="أفضل العروض" FontSize="12" Foreground="#E3F2FD"
                                         HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </Grid>
            </Border>
        </Border>
        <!-- Modern Toolbar -->
        <materialDesign:Card Grid.Row="1" Style="{StaticResource ModernOfferCardStyle}" Margin="20,16,20,0">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Action Buttons Row -->
                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,20">
                    <Button x:Name="AddOfferButton"
                            Style="{StaticResource PrimaryActionButton}"
                            Click="AddOfferButton_Click"
                            Margin="0,0,16,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Plus" Width="18" Height="18" Margin="0,0,8,0"/>
                            <TextBlock Text="إضافة عرض جديد" FontWeight="Medium"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="CompareOffersButton"
                            Style="{StaticResource SecondaryActionButton}"
                            Click="CompareOffersButton_Click"
                            Margin="0,0,16,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Compare" Width="18" Height="18" Margin="0,0,8,0"/>
                            <TextBlock Text="مقارنة العروض" FontWeight="Medium"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="ExportOffersButton"
                            Style="{StaticResource SecondaryActionButton}"
                            Click="ExportOffersButton_Click"
                            Margin="0,0,16,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="FileExport" Width="18" Height="18" Margin="0,0,8,0"/>
                            <TextBlock Text="تصدير" FontWeight="Medium"/>
                        </StackPanel>
                    </Button>

                    <!-- View Toggle -->
                    <ToggleButton x:Name="ViewToggleButton"
                                  Style="{StaticResource MaterialDesignSwitchToggleButton}"
                                  Margin="16,0,0,0"
                                  ToolTip="تبديل العرض: بطاقات/جدول"
                                  Checked="ViewToggleButton_Checked"
                                  Unchecked="ViewToggleButton_Unchecked">
                        <materialDesign:PackIcon Kind="ViewGrid" Width="18" Height="18"/>
                    </ToggleButton>
                </StackPanel>

                <!-- Search and Filters Row -->
                <Grid Grid.Row="1">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="2*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- Enhanced Search Box -->
                    <Border Grid.Column="0" Background="#F8F9FA" CornerRadius="8" Margin="0,0,12,0">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <materialDesign:PackIcon Grid.Column="0" Kind="Magnify"
                                                   Width="20" Height="20"
                                                   Foreground="{StaticResource TextSecondary}"
                                                   Margin="12,0,8,0" VerticalAlignment="Center"/>

                            <TextBox x:Name="SearchTextBox" Grid.Column="1"
                                   Background="Transparent" BorderThickness="0"
                                   materialDesign:HintAssist.Hint="البحث في العروض..."
                                   materialDesign:HintAssist.IsFloating="False"
                                   VerticalAlignment="Center" Padding="0,12"
                                   TextChanged="SearchTextBox_TextChanged"/>

                            <Button x:Name="ClearSearchButton" Grid.Column="2"
                                  Style="{StaticResource MaterialDesignIconButton}"
                                  Width="32" Height="32" Margin="0,0,8,0"
                                  Click="ClearSearchButton_Click"
                                  Visibility="Collapsed">
                                <materialDesign:PackIcon Kind="Close" Width="16" Height="16"/>
                            </Button>
                        </Grid>
                    </Border>

                    <!-- Modern Filter ComboBoxes -->
                    <ComboBox x:Name="FilterScientificNameComboBox" Grid.Column="1"
                            materialDesign:HintAssist.Hint="المادة العلمية"
                            materialDesign:HintAssist.IsFloating="False"
                            Margin="6,0" Height="44"
                            SelectionChanged="FilterComboBox_SelectionChanged"/>

                    <ComboBox x:Name="FilterOfficeComboBox" Grid.Column="2"
                            materialDesign:HintAssist.Hint="المكتب العلمي"
                            materialDesign:HintAssist.IsFloating="False"
                            Margin="6,0" Height="44"
                            SelectionChanged="FilterComboBox_SelectionChanged"/>

                    <ComboBox x:Name="FilterRepComboBox" Grid.Column="3"
                            materialDesign:HintAssist.Hint="المندوب"
                            materialDesign:HintAssist.IsFloating="False"
                            Margin="6,0" Height="44"
                            SelectionChanged="FilterComboBox_SelectionChanged"/>

                    <ComboBox x:Name="SortComboBox" Grid.Column="4"
                            materialDesign:HintAssist.Hint="ترتيب حسب"
                            materialDesign:HintAssist.IsFloating="False"
                            Margin="6,0,12,0" Height="44"
                            SelectionChanged="SortComboBox_SelectionChanged">
                        <ComboBoxItem Content="الأحدث أولاً"/>
                        <ComboBoxItem Content="السعر الأقل"/>
                        <ComboBoxItem Content="السعر الأعلى"/>
                        <ComboBoxItem Content="أفضل عرض"/>
                        <ComboBoxItem Content="المكتب العلمي"/>
                    </ComboBox>

                    <!-- Filter Reset Button -->
                    <Button x:Name="ResetFiltersButton" Grid.Column="5"
                          Style="{StaticResource MaterialDesignIconButton}"
                          Width="44" Height="44"
                          ToolTip="إعادة تعيين الفلاتر"
                          Click="ResetFiltersButton_Click">
                        <materialDesign:PackIcon Kind="FilterRemove" Width="20" Height="20"/>
                    </Button>
                </Grid>
            </Grid>
        </materialDesign:Card>

        <!-- Main Content Area -->
        <Grid Grid.Row="2" Margin="20,16,20,20">
            <!-- Cards View (Default) -->
            <ScrollViewer x:Name="CardsScrollViewer" VerticalScrollBarVisibility="Auto">
                <ItemsControl x:Name="OffersCardsControl">
                    <ItemsControl.ItemsPanel>
                        <ItemsPanelTemplate>
                            <WrapPanel Orientation="Horizontal" ItemWidth="380" ItemHeight="280"/>
                        </ItemsPanelTemplate>
                    </ItemsControl.ItemsPanel>
                    <ItemsControl.ItemTemplate>
                        <DataTemplate>
                            <!-- Modern Offer Card Template -->
                            <materialDesign:Card Style="{StaticResource ModernOfferCardStyle}"
                                               Width="360" Height="260" Margin="8"
                                               Cursor="Hand" MouseLeftButtonUp="OfferCard_Click">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="*"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- Card Header -->
                                    <Grid Grid.Row="0" Margin="0,0,0,12">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <StackPanel Grid.Column="0">
                                            <TextBlock Text="{Binding ScientificName}"
                                                     FontSize="16" FontWeight="Bold"
                                                     Foreground="{StaticResource TextPrimary}"
                                                     TextTrimming="CharacterEllipsis"/>
                                            <TextBlock Text="{Binding TradeName}"
                                                     FontSize="13"
                                                     Foreground="{StaticResource TextSecondary}"
                                                     TextTrimming="CharacterEllipsis"
                                                     Margin="0,2,0,0"/>
                                        </StackPanel>

                                        <!-- Best Offer Badge -->
                                        <Border Grid.Column="1" x:Name="BestOfferBadge"
                                              Background="{StaticResource AccentGreen}"
                                              CornerRadius="12" Padding="8,4"
                                              Visibility="Collapsed">
                                            <TextBlock Text="أفضل عرض" FontSize="10"
                                                     Foreground="White" FontWeight="Bold"/>
                                        </Border>
                                    </Grid>

                                    <!-- Card Content -->
                                    <StackPanel Grid.Row="1">
                                        <Grid Margin="0,0,0,8">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>

                                            <materialDesign:PackIcon Grid.Column="0" Kind="Office"
                                                                   Width="16" Height="16"
                                                                   Foreground="{StaticResource PrimaryBlue}"
                                                                   Margin="0,0,8,0" VerticalAlignment="Center"/>
                                            <TextBlock Grid.Column="1" Text="{Binding ScientificOffice}"
                                                     FontSize="13" Foreground="{StaticResource TextSecondary}"
                                                     TextTrimming="CharacterEllipsis"/>
                                        </Grid>

                                        <Grid Margin="0,0,0,8">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>

                                            <materialDesign:PackIcon Grid.Column="0" Kind="Account"
                                                                   Width="16" Height="16"
                                                                   Foreground="{StaticResource PrimaryBlue}"
                                                                   Margin="0,0,8,0" VerticalAlignment="Center"/>
                                            <TextBlock Grid.Column="1" Text="{Binding RepresentativeName}"
                                                     FontSize="13" Foreground="{StaticResource TextSecondary}"
                                                     TextTrimming="CharacterEllipsis"/>
                                        </Grid>

                                        <Grid Margin="0,0,0,12">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>

                                            <materialDesign:PackIcon Grid.Column="0" Kind="Phone"
                                                                   Width="16" Height="16"
                                                                   Foreground="{StaticResource PrimaryBlue}"
                                                                   Margin="0,0,8,0" VerticalAlignment="Center"/>
                                            <TextBlock Grid.Column="1" Text="{Binding RepresentativePhone}"
                                                     FontSize="13" Foreground="{StaticResource TextSecondary}"/>
                                        </Grid>
                                    </StackPanel>

                                    <!-- Price Section -->
                                    <Grid Grid.Row="2" Margin="0,0,0,12">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <StackPanel Grid.Column="0">
                                            <TextBlock Text="السعر" FontSize="12"
                                                     Foreground="{StaticResource TextSecondary}"/>
                                            <TextBlock Text="{Binding Price, StringFormat='{}{0:N0} د.ع'}"
                                                     FontSize="18" FontWeight="Bold"
                                                     Foreground="{StaticResource PrimaryDark}"/>
                                        </StackPanel>

                                        <StackPanel Grid.Column="1" HorizontalAlignment="Right">
                                            <TextBlock Text="البونص/الخصم" FontSize="12"
                                                     Foreground="{StaticResource TextSecondary}"
                                                     HorizontalAlignment="Right"/>
                                            <TextBlock Text="{Binding BonusOrDiscount}"
                                                     FontSize="13" FontWeight="Medium"
                                                     Foreground="{StaticResource AccentGreen}"
                                                     HorizontalAlignment="Right"/>
                                        </StackPanel>
                                    </Grid>

                                    <!-- Card Footer -->
                                    <Grid Grid.Row="3">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <TextBlock Grid.Column="0"
                                                 Text="{Binding CreatedAt, StringFormat='تاريخ الإدخال: {0:yyyy/MM/dd}'}"
                                                 FontSize="11" Foreground="{StaticResource TextSecondary}"
                                                 VerticalAlignment="Center"/>

                                        <StackPanel Grid.Column="1" Orientation="Horizontal">
                                            <Button Style="{StaticResource MaterialDesignIconButton}"
                                                  Width="32" Height="32" Margin="4,0,0,0"
                                                  ToolTip="تعديل العرض"
                                                  Click="EditOfferButton_Click"
                                                  CommandParameter="{Binding}">
                                                <materialDesign:PackIcon Kind="Edit" Width="16" Height="16"/>
                                            </Button>

                                            <Button Style="{StaticResource MaterialDesignIconButton}"
                                                  Width="32" Height="32" Margin="4,0,0,0"
                                                  ToolTip="حذف العرض"
                                                  Click="DeleteOfferButton_Click"
                                                  CommandParameter="{Binding}">
                                                <materialDesign:PackIcon Kind="Delete" Width="16" Height="16"
                                                                       Foreground="{StaticResource AccentRed}"/>
                                            </Button>
                                        </StackPanel>
                                    </Grid>
                                </Grid>
                            </materialDesign:Card>
                        </DataTemplate>
                    </ItemsControl.ItemTemplate>
                </ItemsControl>
            </ScrollViewer>

            <!-- Table View (Hidden by default) -->
            <materialDesign:Card x:Name="TableViewCard" Style="{StaticResource ModernOfferCardStyle}"
                               Visibility="Collapsed">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Table Header -->
                    <Grid Grid.Row="0" Margin="0,0,0,16">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0">
                            <TextBlock Text="جدول العروض" FontSize="18" FontWeight="Bold"
                                     Foreground="{StaticResource TextPrimary}"/>
                            <TextBlock x:Name="TableResultsText" Text="عرض جميع العروض"
                                     FontSize="13" Foreground="{StaticResource TextSecondary}"
                                     Margin="0,4,0,0"/>
                        </StackPanel>

                        <StackPanel Grid.Column="1" Orientation="Horizontal">
                            <Button Style="{StaticResource MaterialDesignIconButton}"
                                  ToolTip="تحديث البيانات"
                                  Click="RefreshDataButton_Click">
                                <materialDesign:PackIcon Kind="Refresh" Width="20" Height="20"/>
                            </Button>

                            <Button Style="{StaticResource MaterialDesignIconButton}"
                                  ToolTip="طباعة الجدول"
                                  Click="PrintTableButton_Click">
                                <materialDesign:PackIcon Kind="Printer" Width="20" Height="20"/>
                            </Button>
                        </StackPanel>
                    </Grid>

                    <!-- Modern DataGrid -->
                    <DataGrid x:Name="OffersDataGrid" Grid.Row="1"
                            AutoGenerateColumns="False"
                            CanUserAddRows="False"
                            CanUserDeleteRows="False"
                            IsReadOnly="True"
                            SelectionMode="Single"
                            GridLinesVisibility="Horizontal"
                            HeadersVisibility="Column"
                            Background="Transparent"
                            BorderThickness="0"
                            RowHeight="56"
                            FontSize="13"
                            AlternatingRowBackground="#FAFBFC"
                            MouseDoubleClick="OffersDataGrid_MouseDoubleClick">

                        <DataGrid.ColumnHeaderStyle>
                            <Style TargetType="DataGridColumnHeader">
                                <Setter Property="Background" Value="{StaticResource BackgroundLight}"/>
                                <Setter Property="Foreground" Value="{StaticResource TextPrimary}"/>
                                <Setter Property="FontWeight" Value="Bold"/>
                                <Setter Property="FontSize" Value="13"/>
                                <Setter Property="Padding" Value="16,12"/>
                                <Setter Property="BorderThickness" Value="0,0,0,1"/>
                                <Setter Property="BorderBrush" Value="#E0E0E0"/>
                            </Style>
                        </DataGrid.ColumnHeaderStyle>

                        <DataGrid.RowStyle>
                            <Style TargetType="DataGridRow">
                                <Setter Property="Padding" Value="0"/>
                                <Setter Property="Margin" Value="0"/>
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#F0F7FF"/>
                                    </Trigger>
                                    <Trigger Property="IsSelected" Value="True">
                                        <Setter Property="Background" Value="#E3F2FD"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </DataGrid.RowStyle>

                        <DataGrid.CellStyle>
                            <Style TargetType="DataGridCell">
                                <Setter Property="BorderThickness" Value="0"/>
                                <Setter Property="Padding" Value="16,8"/>
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Style.Triggers>
                                    <Trigger Property="IsSelected" Value="True">
                                        <Setter Property="Background" Value="Transparent"/>
                                        <Setter Property="Foreground" Value="{StaticResource TextPrimary}"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </DataGrid.CellStyle>

                        <DataGrid.Columns>
                            <!-- Scientific Material Column -->
                            <DataGridTemplateColumn Header="المادة العلمية" Width="200">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel>
                                            <TextBlock Text="{Binding ScientificName}"
                                                     FontWeight="Medium" FontSize="13"
                                                     Foreground="{StaticResource TextPrimary}"/>
                                            <TextBlock Text="{Binding TradeName}"
                                                     FontSize="11" Foreground="{StaticResource TextSecondary}"
                                                     Margin="0,2,0,0"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <!-- Office and Representative Column -->
                            <DataGridTemplateColumn Header="المكتب والمندوب" Width="220">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel>
                                            <TextBlock Text="{Binding ScientificOffice}"
                                                     FontWeight="Medium" FontSize="13"
                                                     Foreground="{StaticResource TextPrimary}"/>
                                            <StackPanel Orientation="Horizontal" Margin="0,2,0,0">
                                                <materialDesign:PackIcon Kind="Account" Width="12" Height="12"
                                                                       Foreground="{StaticResource TextSecondary}"
                                                                       Margin="0,0,4,0"/>
                                                <TextBlock Text="{Binding RepresentativeName}"
                                                         FontSize="11" Foreground="{StaticResource TextSecondary}"/>
                                            </StackPanel>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <!-- Contact Column -->
                            <DataGridTemplateColumn Header="الاتصال" Width="120">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="Phone" Width="14" Height="14"
                                                                   Foreground="{StaticResource PrimaryBlue}"
                                                                   Margin="0,0,6,0"/>
                                            <TextBlock Text="{Binding RepresentativePhone}"
                                                     FontSize="12" Foreground="{StaticResource TextPrimary}"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <!-- Price Column -->
                            <DataGridTemplateColumn Header="السعر" Width="120">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel HorizontalAlignment="Right">
                                            <TextBlock Text="{Binding Price, StringFormat='{}{0:N0}'}"
                                                     FontWeight="Bold" FontSize="14"
                                                     Foreground="{StaticResource PrimaryDark}"/>
                                            <TextBlock Text="دينار عراقي"
                                                     FontSize="10" Foreground="{StaticResource TextSecondary}"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <!-- Bonus/Discount Column -->
                            <DataGridTemplateColumn Header="البونص/الخصم" Width="140">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <Border Background="#E8F5E8" CornerRadius="4" Padding="8,4"
                                              HorizontalAlignment="Center">
                                            <TextBlock Text="{Binding BonusOrDiscount}"
                                                     FontSize="11" FontWeight="Medium"
                                                     Foreground="{StaticResource AccentGreen}"
                                                     TextAlignment="Center"/>
                                        </Border>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <!-- Date Column -->
                            <DataGridTemplateColumn Header="تاريخ الإدخال" Width="120">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding CreatedAt, StringFormat='{}{0:yyyy/MM/dd}'}"
                                                 FontSize="12" Foreground="{StaticResource TextSecondary}"/>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <!-- Actions Column -->
                            <DataGridTemplateColumn Header="الإجراءات" Width="120">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                            <Button Style="{StaticResource MaterialDesignIconButton}"
                                                  Width="32" Height="32" Margin="2"
                                                  ToolTip="تعديل العرض"
                                                  Click="EditOfferButton_Click"
                                                  CommandParameter="{Binding}">
                                                <materialDesign:PackIcon Kind="Edit" Width="16" Height="16"/>
                                            </Button>

                                            <Button Style="{StaticResource MaterialDesignIconButton}"
                                                  Width="32" Height="32" Margin="2"
                                                  ToolTip="حذف العرض"
                                                  Click="DeleteOfferButton_Click"
                                                  CommandParameter="{Binding}">
                                                <materialDesign:PackIcon Kind="Delete" Width="16" Height="16"
                                                                       Foreground="{StaticResource AccentRed}"/>
                                            </Button>

                                            <Button Style="{StaticResource MaterialDesignIconButton}"
                                                  Width="32" Height="32" Margin="2"
                                                  ToolTip="عرض المرفق"
                                                  Click="ViewAttachmentButton_Click"
                                                  CommandParameter="{Binding}"
                                                  x:Name="AttachmentButton">
                                                <materialDesign:PackIcon Kind="Attachment" Width="16" Height="16"/>
                                            </Button>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </materialDesign:Card>

            <!-- Empty State -->
            <StackPanel x:Name="EmptyStatePanel"
                      HorizontalAlignment="Center" VerticalAlignment="Center"
                      Visibility="Collapsed">
                <materialDesign:PackIcon Kind="EmoticonSad" Width="64" Height="64"
                                       Foreground="{StaticResource TextSecondary}"
                                       HorizontalAlignment="Center"/>
                <TextBlock Text="لا توجد عروض متاحة"
                         FontSize="18" FontWeight="Medium"
                         Foreground="{StaticResource TextSecondary}"
                         HorizontalAlignment="Center" Margin="0,16,0,8"/>
                <TextBlock Text="ابدأ بإضافة عرض جديد من المندوبين"
                         FontSize="14" Foreground="{StaticResource TextSecondary}"
                         HorizontalAlignment="Center" Margin="0,0,0,24"/>
                <Button Style="{StaticResource PrimaryActionButton}"
                      Click="AddOfferButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Plus" Width="18" Height="18" Margin="0,0,8,0"/>
                        <TextBlock Text="إضافة أول عرض"/>
                    </StackPanel>
                </Button>
            </StackPanel>
        </Grid>

        <!-- Enhanced Offer Form Overlay -->
        <controls:OfferFormOverlay x:Name="OfferFormOverlay"
                                 Grid.RowSpan="3"
                                 FormClosed="OfferFormOverlay_FormClosed"/>
    </Grid>
</UserControl>