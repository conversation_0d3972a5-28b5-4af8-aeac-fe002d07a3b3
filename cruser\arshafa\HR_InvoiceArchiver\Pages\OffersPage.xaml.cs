using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Threading.Tasks;
using HR_InvoiceArchiver.Models;
using HR_InvoiceArchiver.Services;
using HR_InvoiceArchiver.Controls;

namespace HR_InvoiceArchiver.Pages
{
    public partial class OffersPage : UserControl
    {
        private readonly OfferService _offerService;
        private ObservableCollection<Offer> _offers = new();
        private ObservableCollection<Offer> _filteredOffers = new();
        private bool _isCardsView = true;
        private string _currentSearchText = "";

        public OffersPage(OfferService offerService)
        {
            InitializeComponent();
            _offerService = offerService;

            // Initialize view
            InitializeView();
            LoadOffers();
            SetupFilters();
        }

        private void InitializeView()
        {
            // Set default view to cards
            ViewToggleButton.IsChecked = false; // Cards view
            UpdateViewMode();

            // Setup search box behavior
            SearchTextBox.TextChanged += SearchTextBox_TextChanged;
        }

        private async void LoadOffers()
        {
            try
            {
                var offers = _offerService.GetAllOffers().ToList();
                _offers = new ObservableCollection<Offer>(offers);
                _filteredOffers = new ObservableCollection<Offer>(offers);

                UpdateDataSources();
                UpdateStatistics();
                UpdateEmptyState();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل العروض: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateDataSources()
        {
            if (_isCardsView)
            {
                OffersCardsControl.ItemsSource = _filteredOffers;
            }
            else
            {
                OffersDataGrid.ItemsSource = _filteredOffers;
            }
        }

        private void UpdateStatistics()
        {
            var totalOffers = _offers.Count;
            var bestOffers = GetBestOffers(_offers.ToList()).Count;

            TotalOffersText.Text = totalOffers.ToString();
            BestOffersText.Text = bestOffers.ToString();

            // Update table results text
            if (_filteredOffers.Count != _offers.Count)
            {
                TableResultsText.Text = $"عرض {_filteredOffers.Count} من أصل {totalOffers} عرض";
            }
            else
            {
                TableResultsText.Text = $"عرض جميع العروض ({totalOffers} عرض)";
            }
        }

        private void UpdateEmptyState()
        {
            bool isEmpty = _filteredOffers.Count == 0;
            EmptyStatePanel.Visibility = isEmpty ? Visibility.Visible : Visibility.Collapsed;

            if (_isCardsView)
            {
                CardsScrollViewer.Visibility = isEmpty ? Visibility.Collapsed : Visibility.Visible;
            }
            else
            {
                TableViewCard.Visibility = isEmpty ? Visibility.Collapsed : Visibility.Visible;
            }
        }

        private void SetupFilters()
        {
            // تعبئة الفلاتر بالقيم الفريدة
            var scientificNames = _offers.Select(o => o.ScientificName).Distinct().OrderBy(x => x).ToList();
            var offices = _offers.Select(o => o.ScientificOffice).Distinct().OrderBy(x => x).ToList();
            var representatives = _offers.Select(o => o.RepresentativeName).Distinct().OrderBy(x => x).ToList();

            FilterScientificNameComboBox.ItemsSource = scientificNames;
            FilterOfficeComboBox.ItemsSource = offices;
            FilterRepComboBox.ItemsSource = representatives;
        }

        private void SearchAndFilter()
        {
            try
            {
                var search = SearchTextBox.Text?.Trim() ?? "";
                var scientificName = FilterScientificNameComboBox.SelectedItem as string;
                var office = FilterOfficeComboBox.SelectedItem as string;
                var rep = FilterRepComboBox.SelectedItem as string;
                var sort = (SortComboBox.SelectedItem as ComboBoxItem)?.Content?.ToString();

                // Update search text and clear button visibility
                _currentSearchText = search;
                ClearSearchButton.Visibility = string.IsNullOrEmpty(search) ? Visibility.Collapsed : Visibility.Visible;

                var searchLower = search.ToLower();
                var filtered = _offers.Where(o =>
                    (string.IsNullOrEmpty(search) ||
                     (o.ScientificOffice?.ToLower().Contains(searchLower) == true) ||
                     (o.RepresentativeName?.ToLower().Contains(searchLower) == true) ||
                     (o.ScientificName?.ToLower().Contains(searchLower) == true) ||
                     (o.TradeName?.ToLower().Contains(searchLower) == true) ||
                     (o.Notes?.ToLower().Contains(searchLower) == true)) &&
                    (string.IsNullOrEmpty(scientificName) || o.ScientificName == scientificName) &&
                    (string.IsNullOrEmpty(office) || o.ScientificOffice == office) &&
                    (string.IsNullOrEmpty(rep) || o.RepresentativeName == rep)
                );

                // Apply sorting
                filtered = sort switch
                {
                    "الأحدث أولاً" => filtered.OrderByDescending(o => o.CreatedAt),
                    "السعر الأقل" => filtered.OrderBy(o => o.Price),
                    "السعر الأعلى" => filtered.OrderByDescending(o => o.Price),
                    "أفضل عرض" => GetBestOffers(filtered.ToList()),
                    "المكتب العلمي" => filtered.OrderBy(o => o.ScientificOffice),
                    _ => filtered.OrderByDescending(o => o.CreatedAt)
                };

                _filteredOffers = new ObservableCollection<Offer>(filtered);
                UpdateDataSources();
                UpdateStatistics();
                UpdateEmptyState();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث والفلترة: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private List<Offer> GetBestOffers(List<Offer> offers)
        {
            // أفضل عرض لكل مادة علمية: أقل سعر + أعلى بونص
            return offers
                .GroupBy(o => o.ScientificName)
                .Select(g => g.OrderBy(o => o.Price).ThenByDescending(o => o.BonusOrDiscount).First())
                .ToList();
        }

        private void UpdateViewMode()
        {
            if (_isCardsView)
            {
                CardsScrollViewer.Visibility = Visibility.Visible;
                TableViewCard.Visibility = Visibility.Collapsed;
            }
            else
            {
                CardsScrollViewer.Visibility = Visibility.Collapsed;
                TableViewCard.Visibility = Visibility.Visible;
            }

            UpdateDataSources();
            UpdateEmptyState();
        }

        // Event Handlers
        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e) => SearchAndFilter();
        private void FilterComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e) => SearchAndFilter();
        private void SortComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e) => SearchAndFilter();

        private void ClearSearchButton_Click(object sender, RoutedEventArgs e)
        {
            SearchTextBox.Text = "";
            SearchAndFilter();
        }

        private void ResetFiltersButton_Click(object sender, RoutedEventArgs e)
        {
            SearchTextBox.Text = "";
            FilterScientificNameComboBox.SelectedItem = null;
            FilterOfficeComboBox.SelectedItem = null;
            FilterRepComboBox.SelectedItem = null;
            SortComboBox.SelectedIndex = 0; // Default to "الأحدث أولاً"
            SearchAndFilter();
        }

        private void ViewToggleButton_Checked(object sender, RoutedEventArgs e)
        {
            _isCardsView = false; // Table view
            UpdateViewMode();
        }

        private void ViewToggleButton_Unchecked(object sender, RoutedEventArgs e)
        {
            _isCardsView = true; // Cards view
            UpdateViewMode();
        }

        private void AddOfferButton_Click(object sender, RoutedEventArgs e)
        {
            OfferFormOverlay.ShowAddOfferForm();
        }

        private void CompareOffersButton_Click(object sender, RoutedEventArgs e)
        {
            // TODO: Implement offers comparison feature
            MessageBox.Show("ميزة مقارنة العروض قيد التطوير", "قريباً",
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ExportOffersButton_Click(object sender, RoutedEventArgs e)
        {
            // TODO: Implement export feature
            MessageBox.Show("ميزة تصدير العروض قيد التطوير", "قريباً",
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void RefreshDataButton_Click(object sender, RoutedEventArgs e)
        {
            LoadOffers();
            SetupFilters();
        }

        private void PrintTableButton_Click(object sender, RoutedEventArgs e)
        {
            // TODO: Implement print feature
            MessageBox.Show("ميزة طباعة الجدول قيد التطوير", "قريباً",
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void OfferCard_Click(object sender, MouseButtonEventArgs e)
        {
            if (sender is FrameworkElement element && element.DataContext is Offer selectedOffer)
            {
                OfferFormOverlay.ShowEditOfferForm(selectedOffer);
            }
        }

        private void EditOfferButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.CommandParameter is Offer selectedOffer)
            {
                OfferFormOverlay.ShowEditOfferForm(selectedOffer);
            }
        }

        private void DeleteOfferButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.CommandParameter is Offer selectedOffer)
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف عرض '{selectedOffer.ScientificName}' من '{selectedOffer.ScientificOffice}'؟",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        _offerService.DeleteOffer(selectedOffer.Id);
                        LoadOffers();
                        SetupFilters();
                        MessageBox.Show("تم حذف العرض بنجاح", "نجح",
                                      MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف العرض: {ex.Message}", "خطأ",
                                      MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        private void ViewAttachmentButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.CommandParameter is Offer selectedOffer)
            {
                if (!string.IsNullOrEmpty(selectedOffer.AttachmentPath))
                {
                    try
                    {
                        System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                        {
                            FileName = selectedOffer.AttachmentPath,
                            UseShellExecute = true
                        });
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في فتح المرفق: {ex.Message}", "خطأ",
                                      MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
                else
                {
                    MessageBox.Show("لا يوجد مرفق لهذا العرض", "تنبيه",
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
        }

        private void OfferFormOverlay_FormClosed(object sender, Controls.OfferFormEventArgs e)
        {
            if (e.Success)
            {
                // إعادة تحميل البيانات بعد الإضافة أو التعديل
                LoadOffers();
                SetupFilters();
            }
        }

        private void OffersDataGrid_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (OffersDataGrid.SelectedItem is Offer selectedOffer)
            {
                OfferFormOverlay.ShowEditOfferForm(selectedOffer);
            }
        }
    }
}