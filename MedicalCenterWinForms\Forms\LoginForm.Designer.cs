#nullable disable
#pragma warning disable CS0649
using MedicalCenterWinForms.Helpers;
using System.Drawing.Drawing2D;

namespace MedicalCenterWinForms.Forms
{
    partial class LoginForm
    {
        private System.ComponentModel.IContainer components = null;

        // Main Layout Components
        private Panel pnlBackground;
        private Panel pnlMainCard;
        private Panel pnlSecurityCard;

        // Header Components
        private Panel pnlHeader;
        private Label lblSecurityIcon;
        private Label lblTitle;
        private Label lblSubtitle;

        // Form Components
        private Panel pnlUsernameContainer;
        private Panel pnlPasswordContainer;
        private TextBox txtUsername;
        private TextBox txtPassword;
        private Label lblUsername;
        private Label lblPassword;
        private CheckBox chkRememberMe;

        // Action Components
        private Button btnLogin;
        private Button btnForgotPassword;
        private Panel pnlLoginIcon;
        private Label lblLoginIcon;

        // Status and Loading
        private Panel pnlStatus;
        private Label lblStatusIcon;
        private Label lblStatusText;
        private ProgressBar progressLoading;

        // Footer
        private Panel pnlFooter;
        private Label lblVersion;
        private Label lblCopyright;

        // Security Features
        private Label lblSecurityTitle;
        private Panel pnlSecurityFeature1;
        private Panel pnlSecurityFeature2;
        private Panel pnlSecurityFeature3;
        private Label lblSecurityIcon1;
        private Label lblSecurityIcon2;
        private Label lblSecurityIcon3;
        private Label lblSecurityText1;
        private Label lblSecurityText2;
        private Label lblSecurityText3;

        // Help Button
        private Button btnHelp;

        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.SuspendLayout();

            // Initialize all components
            InitializeAllComponents();

            // Setup layout
            SetupLayout();

            // Apply styling
            ApplyModernStyling();

            // Add resize event handler to center login card
            this.Resize += (sender, e) => CenterLoginCard();
            this.Load += (sender, e) => CenterLoginCard();

            this.ResumeLayout(false);
        }

        private void InitializeAllComponents()
        {
            // Create all components
            this.pnlBackground = new Panel();
            this.pnlLeftSide = new Panel();
            this.pnlRightSide = new Panel();
            this.pnlLoginCard = new Panel();
            this.pnlBackgroundPattern = new Panel();
            this.pnlHeader = new Panel();

            this.picLogo = new PictureBox();
            this.lblWelcome = new Label();
            this.lblSubtitle = new Label();
            this.lblTitle = new Label();

            this.pnlUsernameContainer = new Panel();
            this.pnlPasswordContainer = new Panel();
            this.txtUsername = new TextBox();
            this.txtPassword = new TextBox();
            this.lblUsername = new Label();
            this.lblPassword = new Label();
            this.chkRememberMe = new CheckBox();

            this.btnLogin = new Button();
            this.btnForgotPassword = new Button();
            this.btnSettings = new Button();
            this.btnExit = new Button();

            this.animationTimer = new System.Windows.Forms.Timer(this.components);
        }

        private void SetupLayout()
        {
            // Form Setup
            this.ClientSize = new Size(1200, 800);
            this.FormBorderStyle = FormBorderStyle.None;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.Name = "LoginForm";
            this.Text = "تسجيل الدخول - نظام إدارة المركز الطبي";
            this.TopMost = true;
            this.ShowInTaskbar = true;
            this.BackColor = MaterialDesignHelper.Colors.Background;

            // Background Panel
            this.pnlBackground.Dock = DockStyle.Fill;
            this.pnlBackground.BackColor = MaterialDesignHelper.Colors.Background;
            this.Controls.Add(this.pnlBackground);

            // Left Side Panel (for background)
            this.pnlLeftSide.Dock = DockStyle.Left;
            this.pnlLeftSide.Width = 600;
            this.pnlLeftSide.BackColor = Color.Transparent;
            this.pnlBackground.Controls.Add(this.pnlLeftSide);

            // Right Side Panel (for login card)
            this.pnlRightSide.Dock = DockStyle.Fill;
            this.pnlRightSide.BackColor = Color.Transparent;
            this.pnlBackground.Controls.Add(this.pnlRightSide);

            SetupBackgroundPanel();
            SetupLoginCard();
        }

        private void SetupBackgroundPanel()
        {
            // Background Pattern Panel
            this.pnlBackgroundPattern.Dock = DockStyle.Fill;
            this.pnlLeftSide.Controls.Add(this.pnlBackgroundPattern);

            // Logo Setup
            this.picLogo.Size = new Size(120, 120);
            this.picLogo.Location = new Point(240, 150);
            this.picLogo.BackColor = Color.Transparent;
            this.picLogo.SizeMode = PictureBoxSizeMode.Zoom;
            this.pnlLeftSide.Controls.Add(this.picLogo);

            // Welcome Label
            this.lblWelcome.Text = "مرحباً بك في";
            this.lblWelcome.Font = ArabicFontHelper.GetArabicTitleFont(28F, FontStyle.Bold);
            this.lblWelcome.ForeColor = Color.White;
            this.lblWelcome.AutoSize = true;
            this.lblWelcome.Location = new Point(200, 300);
            this.lblWelcome.BackColor = Color.Transparent;
            this.pnlLeftSide.Controls.Add(this.lblWelcome);

            // Subtitle Label
            this.lblSubtitle.Text = "نظام إدارة المركز الطبي المتطور";
            this.lblSubtitle.Font = ArabicFontHelper.GetArabicFont(18F);
            this.lblSubtitle.ForeColor = Color.FromArgb(220, 255, 255, 255);
            this.lblSubtitle.AutoSize = true;
            this.lblSubtitle.Location = new Point(150, 350);
            this.lblSubtitle.BackColor = Color.Transparent;
            this.pnlLeftSide.Controls.Add(this.lblSubtitle);
        }

        private void SetupLoginCard()
        {
            // Login Card Panel
            this.pnlLoginCard.Size = new Size(450, 600);
            this.pnlLoginCard.BackColor = Color.White;
            this.pnlLoginCard.Anchor = AnchorStyles.None;
            this.pnlLoginCard.Location = new Point(75, 100);
            this.pnlRightSide.Controls.Add(this.pnlLoginCard);

            // Header Panel
            this.pnlHeader.Dock = DockStyle.Top;
            this.pnlHeader.Height = 120;
            this.pnlHeader.BackColor = Color.Transparent;
            this.pnlLoginCard.Controls.Add(this.pnlHeader);

            // Title
            this.lblTitle.Text = "تسجيل الدخول";
            this.lblTitle.Font = ArabicFontHelper.GetArabicTitleFont(24F, FontStyle.Bold);
            this.lblTitle.ForeColor = MaterialDesignHelper.Colors.Primary;
            this.lblTitle.AutoSize = true;
            this.lblTitle.Location = new Point(150, 40);
            this.lblTitle.TextAlign = ContentAlignment.MiddleCenter;
            this.pnlHeader.Controls.Add(this.lblTitle);

            SetupInputFields();
            SetupButtons();
        }

        private void SetupInputFields()
        {
            // Username Container
            this.pnlUsernameContainer.Location = new Point(50, 150);
            this.pnlUsernameContainer.Size = new Size(350, 60);
            this.pnlUsernameContainer.BackColor = Color.FromArgb(248, 249, 250);
            this.pnlLoginCard.Controls.Add(this.pnlUsernameContainer);

            // Username Label
            this.lblUsername.Text = "اسم المستخدم";
            this.lblUsername.Location = new Point(10, 5);
            this.lblUsername.AutoSize = true;
            this.lblUsername.Font = ArabicFontHelper.GetArabicFont(10F);
            this.lblUsername.ForeColor = Color.FromArgb(108, 117, 125);
            this.pnlUsernameContainer.Controls.Add(this.lblUsername);

            // Username TextBox
            this.txtUsername.Location = new Point(10, 25);
            this.txtUsername.Size = new Size(330, 25);
            this.txtUsername.Text = "admin";
            this.txtUsername.Font = ArabicFontHelper.GetArabicFont(12F);
            this.txtUsername.BorderStyle = BorderStyle.None;
            this.txtUsername.BackColor = Color.FromArgb(248, 249, 250);
            this.txtUsername.RightToLeft = RightToLeft.Yes;
            this.pnlUsernameContainer.Controls.Add(this.txtUsername);

            // Password Container
            this.pnlPasswordContainer.Location = new Point(50, 230);
            this.pnlPasswordContainer.Size = new Size(350, 60);
            this.pnlPasswordContainer.BackColor = Color.FromArgb(248, 249, 250);
            this.pnlLoginCard.Controls.Add(this.pnlPasswordContainer);

            // Password Label
            this.lblPassword.Text = "كلمة المرور";
            this.lblPassword.Location = new Point(10, 5);
            this.lblPassword.AutoSize = true;
            this.lblPassword.Font = ArabicFontHelper.GetArabicFont(10F);
            this.lblPassword.ForeColor = Color.FromArgb(108, 117, 125);
            this.pnlPasswordContainer.Controls.Add(this.lblPassword);

            // Password TextBox
            this.txtPassword.Location = new Point(10, 25);
            this.txtPassword.Size = new Size(330, 25);
            this.txtPassword.Text = "admin123";
            this.txtPassword.Font = ArabicFontHelper.GetArabicFont(12F);
            this.txtPassword.BorderStyle = BorderStyle.None;
            this.txtPassword.UseSystemPasswordChar = true;
            this.txtPassword.BackColor = Color.FromArgb(248, 249, 250);
            this.txtPassword.RightToLeft = RightToLeft.Yes;
            this.pnlPasswordContainer.Controls.Add(this.txtPassword);

            // Remember Me Checkbox
            this.chkRememberMe.Location = new Point(50, 310);
            this.chkRememberMe.Text = "تذكرني";
            this.chkRememberMe.Font = ArabicFontHelper.GetArabicFont(10F);
            this.chkRememberMe.RightToLeft = RightToLeft.Yes;
            this.pnlLoginCard.Controls.Add(this.chkRememberMe);
        }

        private void SetupButtons()
        {
            // Login Button
            this.btnLogin.Location = new Point(50, 360);
            this.btnLogin.Size = new Size(350, 55);
            this.btnLogin.Text = "تسجيل الدخول";
            this.btnLogin.BackColor = Color.FromArgb(0, 123, 255);
            this.btnLogin.ForeColor = Color.White;
            this.btnLogin.FlatStyle = FlatStyle.Flat;
            this.btnLogin.FlatAppearance.BorderSize = 0;
            this.btnLogin.Font = ArabicFontHelper.GetArabicButtonFont(14F, FontStyle.Bold);
            this.btnLogin.Cursor = Cursors.Hand;
            this.btnLogin.Click += new EventHandler(this.btnLogin_Click);
            this.pnlLoginCard.Controls.Add(this.btnLogin);

            // Forgot Password Button
            this.btnForgotPassword.Location = new Point(50, 430);
            this.btnForgotPassword.Size = new Size(150, 35);
            this.btnForgotPassword.Text = "نسيت كلمة المرور؟";
            this.btnForgotPassword.BackColor = Color.Transparent;
            this.btnForgotPassword.ForeColor = Color.FromArgb(0, 123, 255);
            this.btnForgotPassword.FlatStyle = FlatStyle.Flat;
            this.btnForgotPassword.FlatAppearance.BorderSize = 0;
            this.btnForgotPassword.Font = ArabicFontHelper.GetArabicFont(10F);
            this.btnForgotPassword.Cursor = Cursors.Hand;
            this.pnlLoginCard.Controls.Add(this.btnForgotPassword);

            // Settings Button
            this.btnSettings.Location = new Point(220, 430);
            this.btnSettings.Size = new Size(80, 35);
            this.btnSettings.Text = "إعدادات";
            this.btnSettings.BackColor = Color.FromArgb(108, 117, 125);
            this.btnSettings.ForeColor = Color.White;
            this.btnSettings.FlatStyle = FlatStyle.Flat;
            this.btnSettings.FlatAppearance.BorderSize = 0;
            this.btnSettings.Font = ArabicFontHelper.GetArabicFont(10F);
            this.btnSettings.Cursor = Cursors.Hand;
            this.btnSettings.Click += new EventHandler(this.btnSettings_Click);
            this.pnlLoginCard.Controls.Add(this.btnSettings);

            // Exit Button
            this.btnExit.Location = new Point(320, 430);
            this.btnExit.Size = new Size(80, 35);
            this.btnExit.Text = "خروج";
            this.btnExit.BackColor = Color.FromArgb(220, 53, 69);
            this.btnExit.ForeColor = Color.White;
            this.btnExit.FlatStyle = FlatStyle.Flat;
            this.btnExit.FlatAppearance.BorderSize = 0;
            this.btnExit.Font = ArabicFontHelper.GetArabicFont(10F);
            this.btnExit.Cursor = Cursors.Hand;
            this.btnExit.Click += new EventHandler(this.btnExit_Click);
            this.pnlLoginCard.Controls.Add(this.btnExit);
        }

        private void ApplyModernStyling()
        {
            // Apply gradient background to left panel
            this.pnlBackgroundPattern.Paint += (sender, e) =>
            {
                var rect = this.pnlBackgroundPattern.ClientRectangle;
                using var gradientBrush = new LinearGradientBrush(
                    rect,
                    Color.FromArgb(74, 144, 226),
                    Color.FromArgb(80, 170, 200),
                    LinearGradientMode.ForwardDiagonal);

                e.Graphics.FillRectangle(gradientBrush, rect);
            };

            // Apply rounded corners to login card
            this.pnlLoginCard.Paint += (sender, e) =>
            {
                var rect = this.pnlLoginCard.ClientRectangle;
                e.Graphics.SmoothingMode = SmoothingMode.AntiAlias;

                // Draw shadow
                var shadowRect = new Rectangle(rect.X + 5, rect.Y + 5, rect.Width, rect.Height);
                using var shadowBrush = new SolidBrush(Color.FromArgb(50, 0, 0, 0));
                e.Graphics.FillRectangle(shadowBrush, shadowRect);

                // Draw card background
                using var cardBrush = new SolidBrush(Color.White);
                e.Graphics.FillRectangle(cardBrush, rect);
            };

            // Style input containers with borders
            StyleInputContainer(this.pnlUsernameContainer);
            StyleInputContainer(this.pnlPasswordContainer);
        }

        private void StyleInputContainer(Panel container)
        {
            container.Paint += (sender, e) =>
            {
                var rect = container.ClientRectangle;
                using var borderPen = new Pen(Color.FromArgb(206, 212, 218), 1);
                e.Graphics.DrawRectangle(borderPen, 0, 0, rect.Width - 1, rect.Height - 1);
            };
        }

        private void CenterLoginCard()
        {
            if (this.pnlLoginCard != null && this.pnlRightSide != null)
            {
                // Center the card in the right panel
                int cardX = (this.pnlRightSide.Width - this.pnlLoginCard.Width) / 2;
                int cardY = (this.pnlRightSide.Height - this.pnlLoginCard.Height) / 2;
                this.pnlLoginCard.Location = new Point(Math.Max(20, cardX), Math.Max(20, cardY));
            }
        }

        // Animation methods are implemented in the main form class
    }
}
