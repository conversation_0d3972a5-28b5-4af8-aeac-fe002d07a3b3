﻿#pragma checksum "..\..\..\..\Pages\OptimizedDashboardPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "05166F48768F26207EC00642543A5A5E96A4F52F"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using HR_InvoiceArchiver.Controls.Dashboard;
using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace HR_InvoiceArchiver.Pages {
    
    
    /// <summary>
    /// OptimizedDashboardPage
    /// </summary>
    public partial class OptimizedDashboardPage : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 26 "..\..\..\..\Pages\OptimizedDashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid LoadingOverlay;
        
        #line default
        #line hidden
        
        
        #line 28 "..\..\..\..\Pages\OptimizedDashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal MaterialDesignThemes.Wpf.PackIcon LoadingIcon;
        
        #line default
        #line hidden
        
        
        #line 66 "..\..\..\..\Pages\OptimizedDashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WelcomeText;
        
        #line default
        #line hidden
        
        
        #line 68 "..\..\..\..\Pages\OptimizedDashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WelcomeSubText;
        
        #line default
        #line hidden
        
        
        #line 73 "..\..\..\..\Pages\OptimizedDashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LastUpdateText;
        
        #line default
        #line hidden
        
        
        #line 75 "..\..\..\..\Pages\OptimizedDashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshButton;
        
        #line default
        #line hidden
        
        
        #line 88 "..\..\..\..\Pages\OptimizedDashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HR_InvoiceArchiver.Controls.Dashboard.StatisticsCardsControl StatisticsCards;
        
        #line default
        #line hidden
        
        
        #line 98 "..\..\..\..\Pages\OptimizedDashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HR_InvoiceArchiver.Controls.Dashboard.ChartSectionControl ChartSection;
        
        #line default
        #line hidden
        
        
        #line 124 "..\..\..\..\Pages\OptimizedDashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock UnpaidCountText;
        
        #line default
        #line hidden
        
        
        #line 140 "..\..\..\..\Pages\OptimizedDashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PartiallyPaidCountText;
        
        #line default
        #line hidden
        
        
        #line 156 "..\..\..\..\Pages\OptimizedDashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PaidCountText;
        
        #line default
        #line hidden
        
        
        #line 168 "..\..\..\..\Pages\OptimizedDashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PaymentRateText;
        
        #line default
        #line hidden
        
        
        #line 171 "..\..\..\..\Pages\OptimizedDashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar PaymentRateProgress;
        
        #line default
        #line hidden
        
        
        #line 180 "..\..\..\..\Pages\OptimizedDashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HR_InvoiceArchiver.Controls.Dashboard.RecentActivitiesControl RecentActivities;
        
        #line default
        #line hidden
        
        
        #line 195 "..\..\..\..\Pages\OptimizedDashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddInvoiceButton;
        
        #line default
        #line hidden
        
        
        #line 204 "..\..\..\..\Pages\OptimizedDashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddPaymentButton;
        
        #line default
        #line hidden
        
        
        #line 213 "..\..\..\..\Pages\OptimizedDashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ViewReportsButton;
        
        #line default
        #line hidden
        
        
        #line 222 "..\..\..\..\Pages\OptimizedDashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SearchButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/HR_InvoiceArchiver;component/pages/optimizeddashboardpage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Pages\OptimizedDashboardPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal System.Delegate _CreateDelegate(System.Type delegateType, string handler) {
            return System.Delegate.CreateDelegate(delegateType, this, handler);
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.LoadingOverlay = ((System.Windows.Controls.Grid)(target));
            return;
            case 2:
            this.LoadingIcon = ((MaterialDesignThemes.Wpf.PackIcon)(target));
            return;
            case 3:
            this.WelcomeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.WelcomeSubText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.LastUpdateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.RefreshButton = ((System.Windows.Controls.Button)(target));
            
            #line 78 "..\..\..\..\Pages\OptimizedDashboardPage.xaml"
            this.RefreshButton.Click += new System.Windows.RoutedEventHandler(this.RefreshButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.StatisticsCards = ((HR_InvoiceArchiver.Controls.Dashboard.StatisticsCardsControl)(target));
            return;
            case 8:
            this.ChartSection = ((HR_InvoiceArchiver.Controls.Dashboard.ChartSectionControl)(target));
            return;
            case 9:
            this.UnpaidCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.PartiallyPaidCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.PaidCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.PaymentRateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.PaymentRateProgress = ((System.Windows.Controls.ProgressBar)(target));
            return;
            case 14:
            this.RecentActivities = ((HR_InvoiceArchiver.Controls.Dashboard.RecentActivitiesControl)(target));
            return;
            case 15:
            this.AddInvoiceButton = ((System.Windows.Controls.Button)(target));
            
            #line 196 "..\..\..\..\Pages\OptimizedDashboardPage.xaml"
            this.AddInvoiceButton.Click += new System.Windows.RoutedEventHandler(this.AddInvoiceButton_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.AddPaymentButton = ((System.Windows.Controls.Button)(target));
            
            #line 205 "..\..\..\..\Pages\OptimizedDashboardPage.xaml"
            this.AddPaymentButton.Click += new System.Windows.RoutedEventHandler(this.AddPaymentButton_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.ViewReportsButton = ((System.Windows.Controls.Button)(target));
            
            #line 214 "..\..\..\..\Pages\OptimizedDashboardPage.xaml"
            this.ViewReportsButton.Click += new System.Windows.RoutedEventHandler(this.ViewReportsButton_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            this.SearchButton = ((System.Windows.Controls.Button)(target));
            
            #line 223 "..\..\..\..\Pages\OptimizedDashboardPage.xaml"
            this.SearchButton.Click += new System.Windows.RoutedEventHandler(this.SearchButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

